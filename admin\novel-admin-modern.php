<?php
/**
 * Modern Admin Interface for Novel Post Type
 * Inspired by team-system admin design
 * 
 * @package Sekaiplus
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue modern admin styles and scripts for novel post type
 */
function sekaiplus_enqueue_modern_novel_admin() {
    global $post_type, $pagenow;
    
    // Only load on novel post-new.php and post.php pages
    if ($post_type === 'novel' && in_array($pagenow, ['post-new.php', 'post.php'])) {
        // Enqueue media uploader
        wp_enqueue_media();
        
        // Add inline styles and scripts
        add_action('admin_head', 'sekaiplus_modern_novel_admin_styles');
        add_action('admin_footer', 'sekaiplus_modern_novel_admin_scripts');
    }
}
add_action('admin_enqueue_scripts', 'sekaiplus_enqueue_modern_novel_admin');

/**
 * Modern admin styles for novel post type
 */
function sekaiplus_modern_novel_admin_styles() {
    ?>
    <style>
        /* Import Modern Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

        /* CSS Variables for Modern Design */
        :root {
            --sekaiplus-primary: #667eea;
            --sekaiplus-secondary: #764ba2;
            --sekaiplus-success: #28a745;
            --sekaiplus-info: #17a2b8;
            --sekaiplus-warning: #ffc107;
            --sekaiplus-danger: #dc3545;
            --sekaiplus-light: #f8f9fa;
            --sekaiplus-dark: #343a40;
            --sekaiplus-white: #ffffff;
            --sekaiplus-gray-100: #f8f9fa;
            --sekaiplus-gray-200: #e9ecef;
            --sekaiplus-gray-300: #dee2e6;
            --sekaiplus-gray-400: #ced4da;
            --sekaiplus-gray-500: #adb5bd;
            --sekaiplus-gray-600: #6c757d;
            --sekaiplus-gray-700: #495057;
            --sekaiplus-gray-800: #343a40;
            --sekaiplus-gray-900: #212529;
            --sekaiplus-border-radius: 12px;
            --sekaiplus-border-radius-lg: 20px;
            --sekaiplus-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --sekaiplus-shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.15);
            --sekaiplus-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Global Admin Body Styling */
        body.post-type-novel {
            font-family: 'Cairo', 'Tajawal', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: var(--sekaiplus-gray-800);
        }

        /* Admin Content Container */
        body.post-type-novel #wpcontent {
            background: transparent;
            padding: 20px;
            margin-left: 160px;
        }

        body.post-type-novel #wpbody-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--sekaiplus-border-radius-lg);
            box-shadow: var(--sekaiplus-shadow-lg);
            margin: 0;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        /* Decorative Elements */
        body.post-type-novel #wpbody-content::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
            pointer-events: none;
            z-index: 0;
        }

        body.post-type-novel #wpbody-content > * {
            position: relative;
            z-index: 1;
        }

        /* Modern Header */
        body.post-type-novel .wrap h1 {
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.75rem;
            font-weight: 800;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
            letter-spacing: -0.02em;
        }

        body.post-type-novel .wrap h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 4px;
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
            border-radius: 2px;
        }

        /* Page Title Actions */
        body.post-type-novel .page-title-action {
            background: linear-gradient(135deg, var(--sekaiplus-success), #20c997);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--sekaiplus-transition);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        body.post-type-novel .page-title-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
            color: white;
        }

        /* Main Content Layout - Single Column for Steps */
        body.post-type-novel #poststuff {
            display: block;
            margin-top: 40px;
        }

        /* Hide default postbox containers for step layout */
        body.post-type-novel #postbox-container-1 {
            float: none;
            width: 100% !important;
            margin-right: 0;
        }

        body.post-type-novel #postbox-container-2 {
            display: none; /* Hide sidebar for step-by-step */
        }

        /* Post Body Content */
        body.post-type-novel #post-body-content {
            background: var(--sekaiplus-white);
            border-radius: var(--sekaiplus-border-radius);
            box-shadow: var(--sekaiplus-shadow);
            padding: 30px;
            border: none;
            position: relative;
            overflow: hidden;
        }

        body.post-type-novel #post-body-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
        }

        /* Title Input Modern Styling */
        body.post-type-novel #titlediv {
            margin-bottom: 30px;
        }

        body.post-type-novel #titlediv label {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--sekaiplus-gray-700);
            margin-bottom: 10px;
            display: block;
        }

        body.post-type-novel #title {
            font-size: 1.5rem;
            font-weight: 600;
            padding: 20px 25px;
            border: 2px solid var(--sekaiplus-gray-300);
            border-radius: var(--sekaiplus-border-radius);
            background: var(--sekaiplus-gray-100);
            transition: var(--sekaiplus-transition);
            width: 100%;
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        body.post-type-novel #title:focus {
            border-color: var(--sekaiplus-primary);
            background: var(--sekaiplus-white);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            outline: none;
            transform: translateY(-1px);
        }

        /* Editor Container */
        body.post-type-novel #wp-content-wrap {
            border: 2px solid var(--sekaiplus-gray-300);
            border-radius: var(--sekaiplus-border-radius);
            overflow: hidden;
            background: var(--sekaiplus-white);
            box-shadow: var(--sekaiplus-shadow);
        }

        body.post-type-novel .wp-editor-tabs {
            background: var(--sekaiplus-gray-100);
            border-bottom: 1px solid var(--sekaiplus-gray-300);
            padding: 0;
        }

        body.post-type-novel .wp-switch-editor {
            background: transparent;
            border: none;
            padding: 15px 25px;
            font-weight: 600;
            color: var(--sekaiplus-gray-600);
            transition: var(--sekaiplus-transition);
            border-radius: 0;
        }

        body.post-type-novel .wp-switch-editor.switch-tmce {
            background: var(--sekaiplus-white);
            color: var(--sekaiplus-primary);
            border-radius: var(--sekaiplus-border-radius) var(--sekaiplus-border-radius) 0 0;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
        }

        /* Postbox Container Styling */
        body.post-type-novel #postbox-container-1,
        body.post-type-novel #postbox-container-2 {
            width: 100% !important;
        }

        /* Modern Meta Boxes */
        body.post-type-novel .postbox {
            background: var(--sekaiplus-white);
            border: none;
            border-radius: var(--sekaiplus-border-radius);
            box-shadow: var(--sekaiplus-shadow);
            margin-bottom: 30px;
            overflow: hidden;
            transition: var(--sekaiplus-transition);
        }

        body.post-type-novel .postbox:hover {
            transform: translateY(-2px);
            box-shadow: var(--sekaiplus-shadow-lg);
        }

        body.post-type-novel .postbox .hndle {
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
            color: var(--sekaiplus-white);
            padding: 20px 30px;
            font-size: 1.2rem;
            font-weight: 700;
            border: none;
            cursor: default;
            position: relative;
            overflow: hidden;
        }

        body.post-type-novel .postbox .hndle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        body.post-type-novel .postbox:hover .hndle::before {
            left: 100%;
        }

        body.post-type-novel .postbox .inside {
            padding: 30px;
            background: var(--sekaiplus-white);
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            body.post-type-novel #wpcontent {
                margin-left: 36px;
            }

            .novel-steps-container {
                max-width: 700px;
                padding: 15px;
            }
        }

        @media (max-width: 960px) {
            .steps-progress {
                flex-wrap: wrap;
                gap: 10px;
                justify-content: center;
            }

            .step-item {
                margin-bottom: 10px;
            }

            .step-title {
                font-size: 0.7rem;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 782px) {
            body.post-type-novel #wpcontent {
                margin-left: 0;
                padding: 10px;
            }

            body.post-type-novel #wpbody-content {
                padding: 15px;
                border-radius: 10px;
            }

            body.post-type-novel .wrap h1 {
                font-size: 1.8rem;
                margin-bottom: 20px;
            }

            body.post-type-novel #title {
                font-size: 1.1rem;
                padding: 12px 15px;
            }

            .novel-steps-container {
                max-width: 100%;
                padding: 10px;
            }

            .step-content {
                padding: 20px 15px;
            }

            .step-content h3 {
                font-size: 1.3rem;
                margin-bottom: 15px;
            }

            .step-navigation {
                flex-direction: column;
                gap: 10px;
            }

            .step-btn {
                width: 100%;
                justify-content: center;
                padding: 15px;
            }

            .steps-progress {
                margin-bottom: 20px;
                padding: 0 10px;
            }

            .steps-progress::before,
            .steps-progress::after {
                display: none; /* Hide progress line on mobile */
            }

            .step-item {
                flex-direction: row;
                align-items: center;
                gap: 8px;
                background: transparent;
                padding: 5px;
            }

            .step-number {
                margin-bottom: 0;
            }
        }

        @media (max-width: 480px) {
            body.post-type-novel #wpbody-content {
                padding: 10px;
                margin: 10px;
            }

            .novel-steps-container {
                padding: 5px;
            }

            .step-content {
                padding: 15px 10px;
            }

            .novel-meta-field input,
            .novel-meta-field select,
            .novel-meta-field textarea {
                padding: 10px 12px;
                font-size: 0.9rem;
            }

            .step-title {
                font-size: 0.65rem;
                white-space: normal;
                text-align: center;
                line-height: 1.2;
            }

            .add-new-creator {
                padding: 8px 15px;
                font-size: 0.8rem;
                margin-top: 10px;
                display: block;
                text-align: center;
            }

            .volume-cover-item {
                padding: 15px;
                margin-bottom: 15px;
            }

            .button-primary,
            .button-secondary {
                padding: 8px 15px !important;
                font-size: 0.8rem !important;
                margin: 2px !important;
            }
        }
        /* Step-by-Step Progress Bar */
        .novel-steps-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .steps-progress {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
            position: relative;
        }

        .steps-progress::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--sekaiplus-gray-300);
            z-index: 1;
        }

        .steps-progress::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            height: 2px;
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
            z-index: 2;
            transition: width 0.5s ease;
        }

        .step-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 3;
            background: var(--sekaiplus-white);
            padding: 10px;
            border-radius: 50px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--sekaiplus-gray-300);
            color: var(--sekaiplus-gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1rem;
            transition: var(--sekaiplus-transition);
            margin-bottom: 8px;
        }

        .step-item.active .step-number {
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
            color: var(--sekaiplus-white);
            transform: scale(1.1);
        }

        .step-item.completed .step-number {
            background: var(--sekaiplus-success);
            color: var(--sekaiplus-white);
        }

        .step-title {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--sekaiplus-gray-600);
            text-align: center;
            white-space: nowrap;
        }

        .step-item.active .step-title {
            color: var(--sekaiplus-primary);
        }

        /* Step Content */
        .step-content {
            display: none;
            background: var(--sekaiplus-white);
            border-radius: var(--sekaiplus-border-radius);
            padding: 40px;
            box-shadow: var(--sekaiplus-shadow);
            margin-bottom: 30px;
            animation: fadeInUp 0.5s ease;
        }

        .step-content.active {
            display: block;
        }

        .step-content h3 {
            color: var(--sekaiplus-gray-800);
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .step-content h3::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
            border-radius: 2px;
        }

        /* Step Navigation Buttons */
        .step-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            gap: 20px;
        }

        .step-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: var(--sekaiplus-transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .step-btn-prev {
            background: var(--sekaiplus-gray-500);
            color: var(--sekaiplus-white);
        }

        .step-btn-prev:hover {
            background: var(--sekaiplus-gray-600);
            transform: translateY(-2px);
        }

        .step-btn-next {
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
            color: var(--sekaiplus-white);
        }

        .step-btn-next:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .step-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Novel Meta Sections for Steps */
        .novel-meta-section {
            background: transparent;
            border: none;
            padding: 0;
            margin-bottom: 30px;
        }

        .novel-meta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
        }

        .novel-meta-section:hover {
            transform: translateY(-1px);
            box-shadow: var(--sekaiplus-shadow);
        }

        .novel-meta-section h4 {
            color: var(--sekaiplus-gray-800);
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0 0 25px 0;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--sekaiplus-gray-200);
            position: relative;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .novel-meta-section h4::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 60px;
            height: 2px;
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
        }

        .novel-meta-section h4 .dashicons {
            color: var(--sekaiplus-primary);
            font-size: 1.2em;
        }

        /* Modern Field Layout */
        .novel-meta-field {
            display: grid;
            grid-template-columns: 180px 1fr;
            gap: 20px;
            align-items: start;
            margin-bottom: 25px;
            padding: 15px;
            border-radius: 8px;
            transition: var(--sekaiplus-transition);
        }

        .novel-meta-field:hover {
            background: rgba(102, 126, 234, 0.02);
        }

        .novel-meta-field.focused {
            background: rgba(102, 126, 234, 0.05);
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }

        .novel-meta-field label {
            font-weight: 600;
            color: var(--sekaiplus-gray-700);
            font-size: 0.95rem;
            line-height: 1.4;
            padding-top: 8px;
        }

        .novel-meta-field input[type="text"],
        .novel-meta-field input[type="number"],
        .novel-meta-field input[type="date"],
        .novel-meta-field select,
        .novel-meta-field textarea {
            padding: 15px 20px;
            border: 2px solid var(--sekaiplus-gray-300);
            border-radius: 10px;
            background: var(--sekaiplus-white);
            font-size: 0.95rem;
            font-family: 'Cairo', 'Tajawal', sans-serif;
            transition: var(--sekaiplus-transition);
            width: 100%;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .novel-meta-field input:focus,
        .novel-meta-field select:focus,
        .novel-meta-field textarea:focus {
            border-color: var(--sekaiplus-primary);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            outline: none;
            transform: translateY(-1px);
        }

        .novel-meta-field input::placeholder,
        .novel-meta-field textarea::placeholder {
            color: var(--sekaiplus-gray-500);
            font-style: italic;
        }

        /* Creator Fields Styling */
        .creator-field {
            background: var(--sekaiplus-white);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--sekaiplus-gray-200);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: var(--sekaiplus-transition);
        }

        .creator-field:hover {
            box-shadow: var(--sekaiplus-shadow);
        }

        .add-new-creator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, var(--sekaiplus-success), #20c997);
            color: var(--sekaiplus-white);
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: var(--sekaiplus-transition);
            margin-top: 15px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .add-new-creator:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
            color: var(--sekaiplus-white);
        }

        .add-new-creator .dashicons {
            font-size: 16px;
        }

        /* Volume Cover Items */
        .volume-cover-item {
            background: var(--sekaiplus-white);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid var(--sekaiplus-gray-200);
            position: relative;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            transition: var(--sekaiplus-transition);
        }

        .volume-cover-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
        }

        .volume-cover-preview {
            max-width: 150px;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            margin-top: 15px;
            transition: var(--sekaiplus-transition);
        }

        .volume-cover-preview:hover {
            transform: scale(1.05);
        }

        /* Modern Buttons */
        .button-primary {
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary)) !important;
            border: none !important;
            border-radius: 25px !important;
            padding: 12px 25px !important;
            font-weight: 600 !important;
            font-size: 0.95rem !important;
            transition: var(--sekaiplus-transition) !important;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
            text-shadow: none !important;
        }

        .button-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
        }

        .button-secondary {
            background: var(--sekaiplus-gray-600) !important;
            border: none !important;
            border-radius: 25px !important;
            padding: 12px 25px !important;
            color: var(--sekaiplus-white) !important;
            font-weight: 600 !important;
            font-size: 0.95rem !important;
            transition: var(--sekaiplus-transition) !important;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
        }

        .button-secondary:hover {
            background: var(--sekaiplus-gray-700) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
        }

        /* Publish Box Modern Styling */
        body.post-type-novel #submitdiv {
            background: linear-gradient(135deg, var(--sekaiplus-primary), var(--sekaiplus-secondary));
            border-radius: var(--sekaiplus-border-radius);
        }

        body.post-type-novel #submitdiv .hndle {
            background: transparent;
            color: var(--sekaiplus-white);
        }

        body.post-type-novel #submitdiv .inside {
            background: var(--sekaiplus-white);
            margin: 0;
            border-radius: 0 0 var(--sekaiplus-border-radius) var(--sekaiplus-border-radius);
        }

        body.post-type-novel #publishing-action {
            text-align: center;
            padding: 25px;
        }

        body.post-type-novel #publish {
            background: linear-gradient(135deg, var(--sekaiplus-success), #20c997) !important;
            border: none !important;
            color: var(--sekaiplus-white) !important;
            padding: 15px 40px !important;
            border-radius: 30px !important;
            font-weight: 700 !important;
            font-size: 1.1rem !important;
            cursor: pointer !important;
            transition: var(--sekaiplus-transition) !important;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3) !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
        }

        body.post-type-novel #publish:hover {
            transform: translateY(-3px) !important;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4) !important;
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading States */
        .loading {
            position: relative;
            pointer-events: none;
            opacity: 0.7;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid var(--sekaiplus-gray-300);
            border-top: 2px solid var(--sekaiplus-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Success/Error Messages */
        .success-message {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            padding: 15px 25px;
            border-radius: 10px;
            border: 1px solid #c3e6cb;
            margin: 15px 0;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(21, 87, 36, 0.1);
        }

        .error-message {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            padding: 15px 25px;
            border-radius: 10px;
            border: 1px solid #f5c6cb;
            margin: 15px 0;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(114, 28, 36, 0.1);
        }

        /* Mobile Responsive Adjustments */
        @media (max-width: 768px) {
            .novel-meta-field {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .novel-meta-field label {
                padding-top: 0;
            }

            .novel-meta-section {
                padding: 20px;
            }

            .novel-meta-section h4 {
                font-size: 1.1rem;
            }
        }
    </style>
    <?php
}

/**
 * Modern admin scripts for novel post type
 */
function sekaiplus_modern_novel_admin_scripts() {
    ?>
    <script>
    jQuery(document).ready(function($) {
        'use strict';

        // Add fade-in animation to elements
        $('.postbox, .novel-meta-section').addClass('fade-in');

        // Enhanced form validation with modern UI
        $('#post').on('submit', function(e) {
            const title = $('#title').val().trim();
            if (!title) {
                e.preventDefault();
                showNotification('يرجى إدخال عنوان الرواية', 'error');
                $('#title').focus().addClass('error');
                return false;
            }
        });

        // Auto-save indication with modern loading
        $(document).on('heartbeat-send', function() {
            $('#publishing-action').addClass('loading');
        });

        $(document).on('heartbeat-tick', function() {
            $('#publishing-action').removeClass('loading');
        });

        // Focus effects for form fields
        $('.novel-meta-field input, .novel-meta-field select, .novel-meta-field textarea').on('focus', function() {
            $(this).closest('.novel-meta-field').addClass('focused');
        }).on('blur', function() {
            $(this).closest('.novel-meta-field').removeClass('focused');
        });

        // Smooth section navigation
        $('.novel-meta-section h4').on('click', function() {
            const firstInput = $(this).parent().find('.novel-meta-field:first input, .novel-meta-field:first select').first();
            if (firstInput.length) {
                firstInput.focus();
                $('html, body').animate({
                    scrollTop: firstInput.offset().top - 100
                }, 300);
            }
        });

        // Add new creator with AJAX and modern UI
        $('.add-new-creator').on('click', function(e) {
            e.preventDefault();
            const $button = $(this);
            const type = $button.data('type');
            const typeName = type === 'author' ? 'المؤلف' : 'الرسام';

            // Modern prompt dialog
            const name = prompt(`أدخل اسم ${typeName}:`);

            if (name && name.trim()) {
                $button.addClass('loading');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'sekaiplus_add_novel_creator',
                        creator_type: type,
                        creator_name: name.trim(),
                        security: $('#novel_details_nonce').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            const $select = $button.siblings('select');
                            $select.append(`<option value="${response.data.term_id}" selected>${name}</option>`);
                            $select.val(response.data.term_id);
                            showNotification(`تم إضافة ${typeName} بنجاح`, 'success');
                        } else {
                            showNotification(`خطأ: ${response.data.message}`, 'error');
                        }
                    },
                    error: function() {
                        showNotification('حدث خطأ في الاتصال', 'error');
                    },
                    complete: function() {
                        $button.removeClass('loading');
                    }
                });
            }
        });

        // Volume cover management with modern UI
        let volumeIndex = $('.volume-cover-item').length;

        $('#add-volume-cover').on('click', function() {
            const html = `
                <div class="volume-cover-item fade-in">
                    <div class="novel-meta-field">
                        <label>رقم المجلد</label>
                        <input type="number" name="volume_numbers[]" min="1" placeholder="${volumeIndex + 1}">
                    </div>
                    <div class="novel-meta-field">
                        <label>عنوان المجلد</label>
                        <input type="text" name="volume_titles[]" placeholder="عنوان المجلد">
                    </div>
                    <div class="novel-meta-field">
                        <label>صورة الغلاف</label>
                        <div>
                            <input type="hidden" name="volume_covers[]" class="volume-cover-input">
                            <button type="button" class="upload-volume-cover button button-primary">
                                <span class="dashicons dashicons-upload"></span> اختر صورة
                            </button>
                            <button type="button" class="remove-volume-cover button button-secondary">
                                <span class="dashicons dashicons-trash"></span> حذف المجلد
                            </button>
                        </div>
                    </div>
                </div>
            `;

            $('#volume-covers-container').append(html);
            volumeIndex++;

            // Re-apply focus effects to new elements
            $('.volume-cover-item:last .novel-meta-field input').on('focus', function() {
                $(this).closest('.novel-meta-field').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.novel-meta-field').removeClass('focused');
            });
        });

        // Remove volume cover with confirmation
        $(document).on('click', '.remove-volume-cover', function() {
            const $item = $(this).closest('.volume-cover-item');

            if (confirm('هل أنت متأكد من حذف هذا المجلد؟')) {
                $item.fadeOut(300, function() {
                    $(this).remove();
                });
            }
        });

        // Upload volume cover with media library
        $(document).on('click', '.upload-volume-cover', function(e) {
            e.preventDefault();
            const $button = $(this);
            const $input = $button.siblings('.volume-cover-input');

            const mediaUploader = wp.media({
                title: 'اختر صورة الغلاف',
                button: {
                    text: 'استخدام هذه الصورة'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });

            mediaUploader.on('select', function() {
                const attachment = mediaUploader.state().get('selection').first().toJSON();
                $input.val(attachment.url);

                let $preview = $button.parent().find('.volume-cover-preview');
                if ($preview.length) {
                    $preview.attr('src', attachment.url);
                } else {
                    $button.parent().append(`<br><img src="${attachment.url}" class="volume-cover-preview">`);
                }

                showNotification('تم رفع الصورة بنجاح', 'success');
            });

            mediaUploader.open();
        });

        // Modern notification system
        function showNotification(message, type = 'info') {
            const notificationClass = type === 'error' ? 'error-message' : 'success-message';
            const $notification = $(`<div class="${notificationClass}">${message}</div>`);

            // Remove existing notifications
            $('.success-message, .error-message').remove();

            // Add new notification
            $('#wpbody-content').prepend($notification);

            // Auto-hide after 5 seconds
            setTimeout(() => {
                $notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }

        // Enhanced keyboard shortcuts
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + S to save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                $('#publish').click();
            }

            // Escape to close modals/notifications
            if (e.key === 'Escape') {
                $('.success-message, .error-message').fadeOut(300);
            }
        });

        // Auto-resize textareas
        $('textarea').on('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });

        // Character counter for title
        $('#title').on('input', function() {
            const length = $(this).val().length;
            const maxLength = 200; // Adjust as needed

            let $counter = $('#title-counter');
            if (!$counter.length) {
                $counter = $('<div id="title-counter" style="font-size: 0.8rem; color: #6c757d; margin-top: 5px;"></div>');
                $(this).after($counter);
            }

            $counter.text(`${length}/${maxLength} حرف`);

            if (length > maxLength * 0.9) {
                $counter.css('color', '#dc3545');
            } else if (length > maxLength * 0.7) {
                $counter.css('color', '#ffc107');
            } else {
                $counter.css('color', '#6c757d');
            }
        });

        // Initialize tooltips if available
        if (typeof $.fn.tooltip === 'function') {
            $('[data-toggle="tooltip"]').tooltip();
        }

        // Step Navigation Functions
        window.currentStep = 1;
        window.totalSteps = 5;

        window.nextStep = function() {
            if (currentStep < totalSteps) {
                // Validate current step
                if (validateStep(currentStep)) {
                    showStep(currentStep + 1);
                }
            }
        };

        window.prevStep = function() {
            if (currentStep > 1) {
                showStep(currentStep - 1);
            }
        };

        window.showStep = function(step) {
            // Hide all steps
            $('.step-content').removeClass('active');
            $('.step-item').removeClass('active completed');

            // Show current step
            $('#step-' + step).addClass('active');
            $('.step-item[data-step="' + step + '"]').addClass('active');

            // Mark previous steps as completed
            for (let i = 1; i < step; i++) {
                $('.step-item[data-step="' + i + '"]').addClass('completed');
            }

            // Update progress bar
            const progressWidth = ((step - 1) / (totalSteps - 1)) * 100;
            $('.steps-progress::after').css('width', progressWidth + '%');

            currentStep = step;

            // Scroll to top of step
            $('html, body').animate({
                scrollTop: $('.novel-steps-container').offset().top - 50
            }, 300);
        };

        window.validateStep = function(step) {
            let isValid = true;
            let errorMessage = '';

            switch(step) {
                case 1:
                    // At least one title should be filled
                    const titles = $('#step-1 input[type="text"]');
                    let hasTitle = false;
                    titles.each(function() {
                        if ($(this).val().trim()) {
                            hasTitle = true;
                        }
                    });
                    if (!hasTitle) {
                        errorMessage = 'يرجى إدخال عنوان واحد على الأقل';
                        isValid = false;
                    }
                    break;
                case 2:
                    // Optional validation for publication info
                    break;
                case 3:
                    // Optional validation for creators
                    break;
                case 4:
                    // Optional validation for volume covers
                    break;
                case 5:
                    // Optional validation for external links
                    break;
            }

            if (!isValid) {
                showNotification(errorMessage, 'error');
            }

            return isValid;
        };

        // Initialize steps
        showStep(1);

        // Fix volume cover duplication issue
        let volumeIndex = $('.volume-cover-item').length;

        $('#add-volume-cover').off('click').on('click', function() {
            volumeIndex++;
            const html = `
                <div class="volume-cover-item fade-in">
                    <div class="novel-meta-field">
                        <label>رقم المجلد</label>
                        <input type="number" name="volume_numbers[]" min="1" placeholder="${volumeIndex}">
                    </div>
                    <div class="novel-meta-field">
                        <label>عنوان المجلد</label>
                        <input type="text" name="volume_titles[]" placeholder="عنوان المجلد">
                    </div>
                    <div class="novel-meta-field">
                        <label>صورة الغلاف</label>
                        <div>
                            <input type="hidden" name="volume_covers[]" class="volume-cover-input">
                            <button type="button" class="upload-volume-cover button button-primary">
                                <span class="dashicons dashicons-upload"></span> اختر صورة
                            </button>
                            <button type="button" class="remove-volume-cover button button-secondary">
                                <span class="dashicons dashicons-trash"></span> حذف المجلد
                            </button>
                        </div>
                    </div>
                </div>
            `;

            $('#volume-covers-container').append(html);
        });

        console.log('🎨 Sekaiplus Modern Step-by-Step Novel Admin Interface Loaded Successfully!');
    });
    </script>
    <?php
}

/**
 * Enhanced Novel details meta box callback with step-by-step design
 */
function sekaiplus_modern_novel_details_callback($post) {
    wp_nonce_field('sekaiplus_novel_details', 'novel_details_nonce');

    // Get current values
    $japanese_title = get_post_meta($post->ID, '_japanese_title', true);
    $romaji_title = get_post_meta($post->ID, '_romaji_title', true);
    $english_title = get_post_meta($post->ID, '_english_title', true);
    $release_date = get_post_meta($post->ID, '_release_date', true);
    $novel_type = get_post_meta($post->ID, '_novel_type', true);
    $mangaupdates_id = get_post_meta($post->ID, '_mangaupdates_id', true);
    $anilist_id = get_post_meta($post->ID, '_anilist_id', true);
    $volume_covers = get_post_meta($post->ID, '_volume_covers', true) ?: array();

    // Get current authors and illustrators
    $current_authors = wp_get_post_terms($post->ID, 'novel_author', array('fields' => 'ids'));
    $current_illustrators = wp_get_post_terms($post->ID, 'novel_illustrator', array('fields' => 'ids'));

    echo '<div class="novel-steps-container">';

    // Progress Steps
    echo '<div class="steps-progress" id="steps-progress">';
    echo '<div class="step-item active" data-step="1">';
    echo '<div class="step-number">1</div>';
    echo '<div class="step-title">العناوين</div>';
    echo '</div>';

    echo '<div class="step-item" data-step="2">';
    echo '<div class="step-number">2</div>';
    echo '<div class="step-title">معلومات النشر</div>';
    echo '</div>';

    echo '<div class="step-item" data-step="3">';
    echo '<div class="step-number">3</div>';
    echo '<div class="step-title">المؤلف والرسام</div>';
    echo '</div>';

    echo '<div class="step-item" data-step="4">';
    echo '<div class="step-number">4</div>';
    echo '<div class="step-title">أغلفة المجلدات</div>';
    echo '</div>';

    echo '<div class="step-item" data-step="5">';
    echo '<div class="step-number">5</div>';
    echo '<div class="step-title">روابط خارجية</div>';
    echo '</div>';
    echo '</div>';

    // Step 1: Titles
    echo '<div class="step-content active" id="step-1">';
    echo '<h3>العناوين</h3>';

    echo '<div class="novel-meta-field">';
    echo '<label>العنوان الياباني</label>';
    echo '<input type="text" name="japanese_title" value="' . esc_attr($japanese_title) . '" placeholder="例：この素晴らしい世界に祝福を！">';
    echo '</div>';

    echo '<div class="novel-meta-field">';
    echo '<label>العنوان الروماجي</label>';
    echo '<input type="text" name="romaji_title" value="' . esc_attr($romaji_title) . '" placeholder="Kono Subarashii Sekai ni Shukufuku wo!">';
    echo '</div>';

    echo '<div class="novel-meta-field">';
    echo '<label>العنوان الإنجليزي</label>';
    echo '<input type="text" name="english_title" value="' . esc_attr($english_title) . '" placeholder="KonoSuba: God\'s Blessing on This Wonderful World!">';
    echo '</div>';

    echo '<div class="step-navigation">';
    echo '<div></div>'; // Empty div for spacing
    echo '<button type="button" class="step-btn step-btn-next" onclick="nextStep()">التالي <span class="dashicons dashicons-arrow-left-alt2"></span></button>';
    echo '</div>';
    echo '</div>';

    // Step 2: Publication Info
    echo '<div class="step-content" id="step-2">';
    echo '<h3>معلومات النشر</h3>';

    echo '<div class="novel-meta-field">';
    echo '<label>تاريخ الإصدار</label>';
    echo '<input type="date" name="release_date" value="' . esc_attr($release_date) . '">';
    echo '</div>';

    echo '<div class="novel-meta-field">';
    echo '<label>نوع الرواية</label>';
    echo '<select name="novel_type">';
    echo '<option value="">-- اختر النوع --</option>';
    echo '<option value="light_novel"' . selected($novel_type, 'light_novel', false) . '>رواية خفيفة</option>';
    echo '<option value="web_novel"' . selected($novel_type, 'web_novel', false) . '>رواية ويب</option>';
    echo '</select>';
    echo '</div>';

    echo '<div class="step-navigation">';
    echo '<button type="button" class="step-btn step-btn-prev" onclick="prevStep()"><span class="dashicons dashicons-arrow-right-alt2"></span> السابق</button>';
    echo '<button type="button" class="step-btn step-btn-next" onclick="nextStep()">التالي <span class="dashicons dashicons-arrow-left-alt2"></span></button>';
    echo '</div>';
    echo '</div>';

    // Step 3: Creators
    echo '<div class="step-content" id="step-3">';
    echo '<h3>المؤلف والرسام</h3>';

    // Author field
    echo '<div class="novel-meta-field">';
    echo '<label>المؤلف</label>';
    echo '<div>';
    $authors = get_terms(array(
        'taxonomy' => 'novel_author',
        'hide_empty' => false,
    ));
    echo '<select name="novel_author">';
    echo '<option value="">-- اختر المؤلف --</option>';
    foreach ($authors as $author) {
        echo '<option value="' . esc_attr($author->term_id) . '" ' . selected(in_array($author->term_id, $current_authors), true, false) . '>' .
             esc_html($author->name) . '</option>';
    }
    echo '</select>';
    echo '<a href="#" class="add-new-creator" data-type="author"><span class="dashicons dashicons-plus-alt"></span> إضافة مؤلف جديد</a>';
    echo '</div>';
    echo '</div>';

    // Illustrator field
    echo '<div class="novel-meta-field">';
    echo '<label>الرسام</label>';
    echo '<div>';
    $illustrators = get_terms(array(
        'taxonomy' => 'novel_illustrator',
        'hide_empty' => false,
    ));
    echo '<select name="novel_illustrator">';
    echo '<option value="">-- اختر الرسام --</option>';
    foreach ($illustrators as $illustrator) {
        echo '<option value="' . esc_attr($illustrator->term_id) . '" ' . selected(in_array($illustrator->term_id, $current_illustrators), true, false) . '>' .
             esc_html($illustrator->name) . '</option>';
    }
    echo '</select>';
    echo '<a href="#" class="add-new-creator" data-type="illustrator"><span class="dashicons dashicons-plus-alt"></span> إضافة رسام جديد</a>';
    echo '</div>';
    echo '</div>';

    echo '<div class="step-navigation">';
    echo '<button type="button" class="step-btn step-btn-prev" onclick="prevStep()"><span class="dashicons dashicons-arrow-right-alt2"></span> السابق</button>';
    echo '<button type="button" class="step-btn step-btn-next" onclick="nextStep()">التالي <span class="dashicons dashicons-arrow-left-alt2"></span></button>';
    echo '</div>';
    echo '</div>';

    // Step 4: Volume Covers
    echo '<div class="step-content" id="step-4">';
    echo '<h3>أغلفة المجلدات</h3>';
    echo '<div id="volume-covers-container">';
    foreach ($volume_covers as $cover) {
        echo '<div class="volume-cover-item">';
        echo '<div class="novel-meta-field">';
        echo '<label>رقم المجلد</label>';
        echo '<input type="number" name="volume_numbers[]" value="' . esc_attr($cover['number']) . '" min="1" placeholder="1">';
        echo '</div>';

        echo '<div class="novel-meta-field">';
        echo '<label>عنوان المجلد</label>';
        echo '<input type="text" name="volume_titles[]" value="' . esc_attr($cover['title']) . '" placeholder="عنوان المجلد">';
        echo '</div>';

        echo '<div class="novel-meta-field">';
        echo '<label>صورة الغلاف</label>';
        echo '<div>';
        echo '<input type="hidden" name="volume_covers[]" value="' . esc_attr($cover['image']) . '" class="volume-cover-input">';
        echo '<button type="button" class="upload-volume-cover button button-primary"><span class="dashicons dashicons-upload"></span> اختر صورة</button> ';
        echo '<button type="button" class="remove-volume-cover button button-secondary"><span class="dashicons dashicons-trash"></span> حذف المجلد</button>';
        if ($cover['image']) {
            echo '<br><img src="' . esc_url($cover['image']) . '" class="volume-cover-preview">';
        }
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }
    echo '</div>';
    echo '<button type="button" id="add-volume-cover" class="button button-primary"><span class="dashicons dashicons-plus-alt"></span> إضافة مجلد جديد</button>';

    echo '<div class="step-navigation">';
    echo '<button type="button" class="step-btn step-btn-prev" onclick="prevStep()"><span class="dashicons dashicons-arrow-right-alt2"></span> السابق</button>';
    echo '<button type="button" class="step-btn step-btn-next" onclick="nextStep()">التالي <span class="dashicons dashicons-arrow-left-alt2"></span></button>';
    echo '</div>';
    echo '</div>';

    // Step 5: External Links
    echo '<div class="step-content" id="step-5">';
    echo '<h3>روابط خارجية</h3>';

    echo '<div class="novel-meta-field">';
    echo '<label>معرف MangaUpdates</label>';
    echo '<input type="text" name="mangaupdates_id" value="' . esc_attr($mangaupdates_id) . '" placeholder="مثال: klbld7a">';
    echo '</div>';

    echo '<div class="novel-meta-field">';
    echo '<label>معرف AniList</label>';
    echo '<input type="text" name="anilist_id" value="' . esc_attr($anilist_id) . '" placeholder="مثال: 31414">';
    echo '</div>';

    echo '<div class="step-navigation">';
    echo '<button type="button" class="step-btn step-btn-prev" onclick="prevStep()"><span class="dashicons dashicons-arrow-right-alt2"></span> السابق</button>';
    echo '<div></div>'; // Empty div for spacing
    echo '</div>';
    echo '</div>';

    echo '</div>'; // Close novel-steps-container
}

/**
 * Replace the default meta box callback with modern version
 */
function sekaiplus_replace_novel_meta_box() {
    global $post_type;
    if ($post_type === 'novel') {
        // Remove default meta boxes
        remove_meta_box('novel-details', 'novel', 'normal');
        remove_meta_box('novel_authordiv', 'novel', 'side');
        remove_meta_box('novel_illustratordiv', 'novel', 'side');
        remove_meta_box('tagsdiv-novel_author', 'novel', 'side');
        remove_meta_box('tagsdiv-novel_illustrator', 'novel', 'side');

        // Add modern meta box
        add_meta_box(
            'novel-details-modern',
            'تفاصيل الرواية - خطوة بخطوة',
            'sekaiplus_modern_novel_details_callback',
            'novel',
            'normal',
            'high'
        );
    }
}
add_action('add_meta_boxes', 'sekaiplus_replace_novel_meta_box', 20);
